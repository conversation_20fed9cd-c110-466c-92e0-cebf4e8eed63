# -*- coding: utf-8 -*-
# Copyright 2023 Google LLC
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
from __future__ import annotations
from typing import List

from typing_extensions import TypedDict

from google.generativeai import protos
from google.generativeai import string_utils


__all__ = [
    "CitationMetadataDict",
    "CitationSourceDict",
]


class CitationSourceDict(TypedDict):
    start_index: int | None
    end_index: int | None
    uri: str | None
    license: str | None

    __doc__ = string_utils.strip_oneof(protos.CitationSource.__doc__)


class CitationMetadataDict(TypedDict):
    citation_sources: List[CitationSourceDict | None]

    __doc__ = string_utils.strip_oneof(protos.CitationMetadata.__doc__)
